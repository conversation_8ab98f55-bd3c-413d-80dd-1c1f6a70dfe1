# WPF样式规范

## 📋 概述

本文档定义了WPF应用程序中XAML样式的标准化规范，包括样式定义、命名约定、资源字典组织结构和最佳实践。

## 🎯 核心原则

1. **一致性** - 所有样式遵循统一的命名和结构规范
2. **可维护性** - 样式易于理解、修改和扩展
3. **可重用性** - 样式可在不同控件和场景中复用
4. **性能优化** - 避免不必要的样式继承和复杂选择器
5. **主题支持** - 支持多主题切换和自定义

## 📁 资源字典组织结构

### 标准目录结构
```
Styles/
├── App.xaml                    # 应用程序级别样式
├── Themes/                     # 主题相关样式
│   ├── Generic.xaml           # 默认主题
│   ├── Dark.xaml              # 深色主题
│   └── Light.xaml             # 浅色主题
├── Controls/                   # 控件样式
│   ├── Button.xaml            # 按钮样式
│   ├── TextBox.xaml           # 文本框样式
│   ├── DataGrid.xaml          # 数据网格样式
│   └── ...                    # 其他控件样式
├── Colors.xaml                 # 颜色定义
├── Fonts.xaml                  # 字体定义
├── Brushes.xaml               # 画刷定义
└── Animations.xaml            # 动画定义
```

### 资源字典引用顺序
```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <!-- 1. 基础资源 -->
            <ResourceDictionary Source="Styles/Colors.xaml"/>
            <ResourceDictionary Source="Styles/Fonts.xaml"/>
            <ResourceDictionary Source="Styles/Brushes.xaml"/>
            
            <!-- 2. 动画资源 -->
            <ResourceDictionary Source="Styles/Animations.xaml"/>
            
            <!-- 3. 控件样式 -->
            <ResourceDictionary Source="Styles/Controls/Button.xaml"/>
            <ResourceDictionary Source="Styles/Controls/TextBox.xaml"/>
            
            <!-- 4. 主题样式 -->
            <ResourceDictionary Source="Styles/Themes/Generic.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

## 🏷️ 命名约定

### 样式命名规范

#### 基础样式命名
```xml
<!-- 格式：{ControlType}Style -->
<Style x:Key="ButtonStyle" TargetType="Button">
    <!-- 样式定义 -->
</Style>

<!-- 格式：{Purpose}{ControlType}Style -->
<Style x:Key="PrimaryButtonStyle" TargetType="Button">
    <!-- 样式定义 -->
</Style>

<Style x:Key="SecondaryButtonStyle" TargetType="Button">
    <!-- 样式定义 -->
</Style>
```

#### 特殊用途样式命名
```xml
<!-- 格式：{Context}{ControlType}Style -->
<Style x:Key="HeaderButtonStyle" TargetType="Button">
    <!-- 头部按钮样式 -->
</Style>

<Style x:Key="ToolbarButtonStyle" TargetType="Button">
    <!-- 工具栏按钮样式 -->
</Style>

<Style x:Key="DialogButtonStyle" TargetType="Button">
    <!-- 对话框按钮样式 -->
</Style>
```

#### 状态相关样式命名
```xml
<!-- 格式：{State}{ControlType}Style -->
<Style x:Key="DisabledButtonStyle" TargetType="Button">
    <!-- 禁用状态按钮样式 -->
</Style>

<Style x:Key="SelectedListItemStyle" TargetType="ListBoxItem">
    <!-- 选中状态列表项样式 -->
</Style>
```

### 资源命名规范

#### 颜色命名
```xml
<!-- 语义化命名 -->
<Color x:Key="PrimaryColor">#007ACC</Color>
<Color x:Key="SecondaryColor">#6C757D</Color>
<Color x:Key="SuccessColor">#28A745</Color>
<Color x:Key="WarningColor">#FFC107</Color>
<Color x:Key="ErrorColor">#DC3545</Color>

<!-- 功能性命名 -->
<Color x:Key="BackgroundColor">#FFFFFF</Color>
<Color x:Key="ForegroundColor">#212529</Color>
<Color x:Key="BorderColor">#DEE2E6</Color>
<Color x:Key="HoverColor">#E9ECEF</Color>
```

#### 画刷命名
```xml
<!-- 格式：{Purpose}Brush -->
<SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
<SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>

<!-- 渐变画刷 -->
<LinearGradientBrush x:Key="HeaderGradientBrush" StartPoint="0,0" EndPoint="0,1">
    <GradientStop Color="#007ACC" Offset="0"/>
    <GradientStop Color="#005A9B" Offset="1"/>
</LinearGradientBrush>
```

#### 字体命名
```xml
<!-- 格式：{Size/Weight}Font -->
<FontFamily x:Key="DefaultFont">Segoe UI</FontFamily>
<FontFamily x:Key="MonospaceFont">Consolas</FontFamily>

<sys:Double x:Key="SmallFontSize">12</sys:Double>
<sys:Double x:Key="NormalFontSize">14</sys:Double>
<sys:Double x:Key="LargeFontSize">16</sys:Double>
<sys:Double x:Key="HeaderFontSize">20</sys:Double>

<FontWeight x:Key="NormalFontWeight">Normal</FontWeight>
<FontWeight x:Key="BoldFontWeight">Bold</FontWeight>
```

## 🎨 样式定义标准

### 基础样式模板
```xml
<Style x:Key="BaseButtonStyle" TargetType="Button">
    <!-- 基础属性设置 -->
    <Setter Property="FontFamily" Value="{StaticResource DefaultFont}"/>
    <Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
    <Setter Property="FontWeight" Value="{StaticResource NormalFontWeight}"/>
    <Setter Property="Padding" Value="12,6"/>
    <Setter Property="Margin" Value="4"/>
    <Setter Property="MinWidth" Value="80"/>
    <Setter Property="MinHeight" Value="32"/>
    
    <!-- 外观设置 -->
    <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="Foreground" Value="White"/>
    <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="BorderThickness" Value="1"/>
    
    <!-- 模板设置 -->
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Name="Border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="4">
                    <ContentPresenter HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Margin="{TemplateBinding Padding}"/>
                </Border>
                
                <!-- 触发器 -->
                <ControlTemplate.Triggers>
                    <Trigger Property="IsMouseOver" Value="True">
                        <Setter TargetName="Border" Property="Background" 
                                Value="{StaticResource HoverBrush}"/>
                    </Trigger>
                    
                    <Trigger Property="IsPressed" Value="True">
                        <Setter TargetName="Border" Property="Background" 
                                Value="{StaticResource PressedBrush}"/>
                    </Trigger>
                    
                    <Trigger Property="IsEnabled" Value="False">
                        <Setter TargetName="Border" Property="Background" 
                                Value="{StaticResource DisabledBrush}"/>
                        <Setter Property="Foreground" 
                                Value="{StaticResource DisabledForegroundBrush}"/>
                    </Trigger>
                </ControlTemplate.Triggers>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

### 样式继承
```xml
<!-- 基础样式 -->
<Style x:Key="BaseButtonStyle" TargetType="Button">
    <!-- 基础设置 -->
</Style>

<!-- 继承样式 -->
<Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
    <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
    <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
</Style>

<Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
    <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
    <Setter Property="BorderBrush" Value="{StaticResource SecondaryBrush}"/>
</Style>
```

## 🎭 主题支持

### 主题切换机制
```xml
<!-- App.xaml.cs -->
public partial class App : Application
{
    public void ChangeTheme(string themeName)
    {
        var themeUri = new Uri($"Styles/Themes/{themeName}.xaml", UriKind.Relative);
        var themeDict = new ResourceDictionary { Source = themeUri };
        
        // 移除旧主题
        var oldTheme = Resources.MergedDictionaries
            .FirstOrDefault(d => d.Source?.OriginalString.Contains("Themes/") == true);
        if (oldTheme != null)
            Resources.MergedDictionaries.Remove(oldTheme);
        
        // 添加新主题
        Resources.MergedDictionaries.Add(themeDict);
    }
}
```

### 主题资源定义
```xml
<!-- Themes/Dark.xaml -->
<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- 深色主题颜色 -->
    <Color x:Key="BackgroundColor">#2D2D30</Color>
    <Color x:Key="ForegroundColor">#F1F1F1</Color>
    <Color x:Key="BorderColor">#3F3F46</Color>
    <Color x:Key="HoverColor">#3E3E42</Color>
    
    <!-- 深色主题画刷 -->
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="ForegroundBrush" Color="{StaticResource ForegroundColor}"/>
    
</ResourceDictionary>
```

## ⚡ 性能优化建议

### 1. 避免过度嵌套
```xml
<!-- ❌ 不推荐：过度嵌套 -->
<Style x:Key="BadStyle" TargetType="Button">
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Grid>
                    <Border>
                        <Grid>
                            <Border>
                                <ContentPresenter/>
                            </Border>
                        </Grid>
                    </Border>
                </Grid>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>

<!-- ✅ 推荐：简洁结构 -->
<Style x:Key="GoodStyle" TargetType="Button">
    <Setter Property="Template">
        <Setter.Value>
            <ControlTemplate TargetType="Button">
                <Border Background="{TemplateBinding Background}">
                    <ContentPresenter/>
                </Border>
            </ControlTemplate>
        </Setter.Value>
    </Setter>
</Style>
```

### 2. 合理使用资源引用
```xml
<!-- ✅ 推荐：使用StaticResource -->
<Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>

<!-- ❌ 避免：不必要的DynamicResource -->
<Setter Property="Background" Value="{DynamicResource PrimaryBrush}"/>
```

### 3. 优化触发器
```xml
<!-- ✅ 推荐：简单触发器 -->
<Trigger Property="IsMouseOver" Value="True">
    <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
</Trigger>

<!-- ❌ 避免：复杂的多条件触发器 -->
<MultiTrigger>
    <MultiTrigger.Conditions>
        <Condition Property="IsMouseOver" Value="True"/>
        <Condition Property="IsPressed" Value="False"/>
        <Condition Property="IsEnabled" Value="True"/>
    </MultiTrigger.Conditions>
    <Setter Property="Background" Value="{StaticResource HoverBrush}"/>
</MultiTrigger>
```

## 🚫 常见反模式

### 1. 硬编码值
```xml
<!-- ❌ 不推荐：硬编码 -->
<Setter Property="Background" Value="#007ACC"/>
<Setter Property="FontSize" Value="14"/>

<!-- ✅ 推荐：使用资源 -->
<Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
<Setter Property="FontSize" Value="{StaticResource NormalFontSize}"/>
```

### 2. 重复样式定义
```xml
<!-- ❌ 不推荐：重复定义 -->
<Style x:Key="Button1Style" TargetType="Button">
    <Setter Property="FontFamily" Value="Segoe UI"/>
    <Setter Property="FontSize" Value="14"/>
    <Setter Property="Padding" Value="12,6"/>
</Style>

<Style x:Key="Button2Style" TargetType="Button">
    <Setter Property="FontFamily" Value="Segoe UI"/>
    <Setter Property="FontSize" Value="14"/>
    <Setter Property="Padding" Value="12,6"/>
</Style>

<!-- ✅ 推荐：使用继承 -->
<Style x:Key="BaseButtonStyle" TargetType="Button">
    <Setter Property="FontFamily" Value="Segoe UI"/>
    <Setter Property="FontSize" Value="14"/>
    <Setter Property="Padding" Value="12,6"/>
</Style>

<Style x:Key="Button1Style" TargetType="Button" BasedOn="{StaticResource BaseButtonStyle}">
    <!-- 特定设置 -->
</Style>
```

## 📋 检查清单

在创建或审查样式时，请确保：

- [ ] 遵循命名约定
- [ ] 使用资源引用而非硬编码值
- [ ] 合理使用样式继承
- [ ] 支持主题切换
- [ ] 包含必要的状态触发器
- [ ] 优化性能，避免过度嵌套
- [ ] 提供完整的可访问性支持
- [ ] 文档化特殊用途和限制

---

**版本**: 1.0.0  
**更新日期**: 2025-06-19  
**下一步**: 查看 [02-自定义控件开发规范.md](02-自定义控件开发规范.md)
